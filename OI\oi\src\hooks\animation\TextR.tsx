"use client";
import React from "react";
import { motion } from "framer-motion";

const DURATION = 0.25;
const STAGGER = 0.025;

interface RevealTextProps {
  children: string;
  as?: keyof JSX.IntrinsicElements; // h1, p, span, etc.
  className?: string;
}

export const RevealText = ({ children, as = "span", className }: RevealTextProps) => {
  const Tag = as; // dynamic element (like h1, p...)

  return (
    <motion.div
      initial="initial"
      whileInView="visible"
      viewport={{ once: true, amount: 0.6 }}
      className={`relative block overflow-hidden whitespace-nowrap ${className || ""}`}
      style={{
        lineHeight: 0.75,
      }}
    >
      {/* Top text sliding up */}
      <Tag>
        {children.split("").map((l, i) => (
          <motion.span
            key={i}
            variants={{
              initial: { y: 0 },
              visible: { y: "-100%" },
            }}
            transition={{
              duration: DURATION,
              ease: "easeInOut",
              delay: STAGGER * i,
            }}
            className="inline-block"
          >
            {l}
          </motion.span>
        ))}
      </Tag>

      {/* Bottom text sliding into place */}
      <div className="absolute inset-0">
        <Tag>
          {children.split("").map((l, i) => (
            <motion.span
              key={i}
              variants={{
                initial: { y: "100%" },
                visible: { y: 0 },
              }}
              transition={{
                duration: DURATION,
                ease: "easeInOut",
                delay: STAGGER * i,
              }}
              className="inline-block"
            >
              {l}
            </motion.span>
          ))}
        </Tag>
      </div>
    </motion.div>
  );
};
