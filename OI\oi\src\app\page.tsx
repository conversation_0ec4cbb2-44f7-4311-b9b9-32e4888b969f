'use client';

import { useState } from 'react';
import Loading from "@/components/Loading";
import Hero from "@/components/Hero";
import Intro from "@/components/Intro";

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);

  if (isLoading) {
    return <Loading onLoadingComplete={() => setIsLoading(false)} />;
  }

  return (
    <div className="bg-white text-black min-h-screen opacity-0 animate-fadeIn" style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}>
      <Hero />
      <Intro />
    </div>
  );
}