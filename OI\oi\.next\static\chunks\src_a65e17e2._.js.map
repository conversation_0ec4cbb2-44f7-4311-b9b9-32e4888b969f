{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Loading.tsx"], "sourcesContent": ["// components/Loading.tsx\r\nimport React, { useState, useEffect } from 'react';\r\n\r\ninterface LoadingProps {\r\n  onLoadingComplete: () => void;\r\n}\r\n\r\nconst Loading: React.FC<LoadingProps> = ({ onLoadingComplete }) => {\r\n  const [progress, setProgress] = useState(0);\r\n  const [isComplete, setIsComplete] = useState(false);\r\n  const [isZooming, setIsZooming] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const duration = 3000; // 3 seconds total loading time\r\n    const interval = 30; // Update every 30ms for smoother animation\r\n    const steps = duration / interval;\r\n    const increment = 100 / steps;\r\n\r\n    let currentProgress = 0;\r\n\r\n    const timer = setInterval(() => {\r\n      currentProgress += increment;\r\n      \r\n      if (currentProgress >= 100) {\r\n        clearInterval(timer);\r\n        setProgress(100);\r\n        setIsComplete(true);\r\n        \r\n        // Start zoom animation after progress completes\r\n        setTimeout(() => {\r\n          setIsZooming(true);\r\n        }, 300);\r\n        \r\n        // Call onLoadingComplete after zoom animation\r\n        setTimeout(() => {\r\n          onLoadingComplete();\r\n        }, 1300); // 300ms delay + 1000ms zoom animation\r\n        return;\r\n      }\r\n      \r\n      // Add some easing to make it feel more natural\r\n      const eased = easeOutQuart(currentProgress / 100) * 100;\r\n      setProgress(eased);\r\n    }, interval);\r\n\r\n    return () => clearInterval(timer);\r\n  }, [onLoadingComplete]);\r\n\r\n  // Easing function for smooth animation\r\n  const easeOutQuart = (t: number): number => {\r\n    return 1 - Math.pow(1 - t, 4);\r\n  };\r\n\r\n  return (\r\n    <div className={`fixed inset-0 bg-black flex items-center justify-center z-50 transition-all duration-1000 ${\r\n      isZooming ? 'scale-[50] bg-white' : 'scale-100'\r\n    } ${isComplete && !isZooming ? 'opacity-100' : isComplete ? 'opacity-100' : 'opacity-100'}`}>\r\n      <div className=\"relative w-full max-w-lg px-8\">\r\n        {/* Progress Counter */}\r\n        <div className={`absolute bottom-20 left-8 transition-opacity duration-300 ${\r\n          isZooming ? 'opacity-0' : 'opacity-100'\r\n        }`}>\r\n          <span \r\n            className=\"text-white font-medium tracking-tight select-none\"\r\n            style={{ \r\n              fontFamily: 'Aeonik-Medium, sans-serif',\r\n              fontSize: 'clamp(4rem, 12vw, 8rem)',\r\n              lineHeight: '0.8',\r\n              fontFeatureSettings: '\"tnum\" 1'\r\n            }}\r\n          >\r\n            {Math.floor(progress).toString().padStart(3, '0')}\r\n          </span>\r\n        </div>\r\n\r\n        {/* Progress Bar Container */}\r\n        <div className=\"relative w-full h-0.5 bg-gray-700 mx-auto\">\r\n          {/* Progress Bar Fill */}\r\n          <div \r\n            className={`absolute top-0 left-0 h-full bg-white transition-all duration-75 ease-out ${\r\n              isZooming ? 'scale-y-[2000] origin-center' : ''\r\n            }`}\r\n            style={{ \r\n              width: `${progress}%`,\r\n              transformOrigin: 'left center',\r\n              boxShadow: '0 0 10px rgba(255,255,255,0.3)'\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Loading Text */}\r\n        <div className={`absolute top-20 left-8 transition-opacity duration-300 ${\r\n          isZooming ? 'opacity-0' : 'opacity-100'\r\n        }`}>\r\n          <div \r\n            className=\"text-white text-xs font-normal tracking-widest opacity-50 select-none\"\r\n            style={{ \r\n              fontFamily: 'Aeonik-Regular, sans-serif',\r\n              letterSpacing: '0.2em'\r\n            }}\r\n          >\r\n            LOADING\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Subtle background effect */}\r\n      <div className={`absolute inset-0 overflow-hidden pointer-events-none transition-opacity duration-300 ${\r\n        isZooming ? 'opacity-0' : 'opacity-100'\r\n      }`}>\r\n        <div \r\n          className=\"absolute inset-0 transition-opacity duration-2000\"\r\n          style={{\r\n            background: `linear-gradient(90deg, transparent ${progress - 20}%, rgba(255,255,255,0.03) ${progress}%, transparent ${progress + 20}%)`,\r\n            opacity: progress > 10 ? 1 : 0\r\n          }}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Loading;"], "names": [], "mappings": "AAAA,yBAAyB;;;;;AACzB;;;;AAMA,MAAM,UAAkC;QAAC,EAAE,iBAAiB,EAAE;;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM,WAAW,MAAM,+BAA+B;YACtD,MAAM,WAAW,IAAI,2CAA2C;YAChE,MAAM,QAAQ,WAAW;YACzB,MAAM,YAAY,MAAM;YAExB,IAAI,kBAAkB;YAEtB,MAAM,QAAQ;2CAAY;oBACxB,mBAAmB;oBAEnB,IAAI,mBAAmB,KAAK;wBAC1B,cAAc;wBACd,YAAY;wBACZ,cAAc;wBAEd,gDAAgD;wBAChD;uDAAW;gCACT,aAAa;4BACf;sDAAG;wBAEH,8CAA8C;wBAC9C;uDAAW;gCACT;4BACF;sDAAG,OAAO,sCAAsC;wBAChD;oBACF;oBAEA,+CAA+C;oBAC/C,MAAM,QAAQ,aAAa,kBAAkB,OAAO;oBACpD,YAAY;gBACd;0CAAG;YAEH;qCAAO,IAAM,cAAc;;QAC7B;4BAAG;QAAC;KAAkB;IAEtB,uCAAuC;IACvC,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IAC7B;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,6FAEb,OADF,YAAY,wBAAwB,aACrC,KAAyF,OAAtF,cAAc,CAAC,YAAY,gBAAgB,aAAa,gBAAgB;;0BAC1E,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAW,AAAC,6DAEhB,OADC,YAAY,cAAc;kCAE1B,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,UAAU;gCACV,YAAY;gCACZ,qBAAqB;4BACvB;sCAEC,KAAK,KAAK,CAAC,UAAU,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;kCAKjD,6LAAC;wBAAI,WAAU;kCAEb,cAAA,6LAAC;4BACC,WAAW,AAAC,6EAEX,OADC,YAAY,iCAAiC;4BAE/C,OAAO;gCACL,OAAO,AAAC,GAAW,OAAT,UAAS;gCACnB,iBAAiB;gCACjB,WAAW;4BACb;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAW,AAAC,0DAEhB,OADC,YAAY,cAAc;kCAE1B,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,eAAe;4BACjB;sCACD;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAW,AAAC,wFAEhB,OADC,YAAY,cAAc;0BAE1B,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY,AAAC,sCAA+E,OAA1C,WAAW,IAAG,8BAAsD,OAA1B,UAAS,mBAA+B,OAAd,WAAW,IAAG;wBACpI,SAAS,WAAW,KAAK,IAAI;oBAC/B;;;;;;;;;;;;;;;;;AAKV;GAjHM;KAAA;uCAmHS", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from \"next/image\";\nimport Loading from \"@/components/Loading\";\n\nexport default function Home() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  if (isLoading) {\n    return <Loading onLoadingComplete={() => setIsLoading(false)} />;\n  }\n\n  return (\n    <div className=\"bg-white text-black grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 opacity-0 animate-fadeIn\" style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}>\n      <main className=\"flex flex-col gap-[32px] row-start-2 items-center sm:items-start\">\n        <Image\n          className=\"invert-0 dark:invert-0\"\n          src=\"/next.svg\"\n          alt=\"Next.js logo\"\n          width={180}\n          height={38}\n          priority\n        />\n        <ol className=\"list-inside list-decimal text-sm/6 text-center sm:text-left\" style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}>\n          <li className=\"mb-2 tracking-[-.01em]\">\n            Get started by editing{\" \"}\n            <code className=\"bg-gray-100 font-medium px-1 py-0.5 rounded\" style={{ fontFamily: 'Aeonik-Medium, monospace' }}>\n              src/app/page.tsx\n            </code>\n            .\n          </li>\n          <li className=\"tracking-[-.01em]\">\n            Save and see your changes instantly.\n          </li>\n        </ol>\n\n        <div className=\"flex gap-4 items-center flex-col sm:flex-row\">\n          <a\n            className=\"rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-black text-white gap-2 hover:bg-gray-800 font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto\"\n            href=\"https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}\n          >\n            <Image\n              className=\"invert\"\n              src=\"/vercel.svg\"\n              alt=\"Vercel logomark\"\n              width={20}\n              height={20}\n            />\n            Deploy now\n          </a>\n          <a\n            className=\"rounded-full border border-solid border-gray-300 transition-colors flex items-center justify-center hover:bg-gray-50 hover:border-gray-400 font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]\"\n            href=\"https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}\n          >\n            Read our docs\n          </a>\n        </div>\n      </main>\n      <footer className=\"row-start-3 flex gap-[24px] flex-wrap items-center justify-center\">\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/file.svg\"\n            alt=\"File icon\"\n            width={16}\n            height={16}\n          />\n          Learn\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/window.svg\"\n            alt=\"Window icon\"\n            width={16}\n            height={16}\n          />\n          Examples\n        </a>\n        <a\n          className=\"flex items-center gap-2 hover:underline hover:underline-offset-4\"\n          href=\"https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app\"\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n        >\n          <Image\n            aria-hidden\n            src=\"/globe.svg\"\n            alt=\"Globe icon\"\n            width={16}\n            height={16}\n          />\n          Go to nextjs.org →\n        </a>\n      </footer>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,WAAW;QACb,qBAAO,6LAAC,gIAAA,CAAA,UAAO;YAAC,mBAAmB,IAAM,aAAa;;;;;;IACxD;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAsJ,OAAO;YAAE,YAAY;QAA6B;;0BACrN,6LAAC;gBAAK,WAAU;;kCACd,6LAAC,gIAAA,CAAA,UAAK;wBACJ,WAAU;wBACV,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,QAAQ;;;;;;kCAEV,6LAAC;wBAAG,WAAU;wBAA8D,OAAO;4BAAE,YAAY;wBAA6B;;0CAC5H,6LAAC;gCAAG,WAAU;;oCAAyB;oCACd;kDACvB,6LAAC;wCAAK,WAAU;wCAA8C,OAAO;4CAAE,YAAY;wCAA2B;kDAAG;;;;;;oCAE1G;;;;;;;0CAGT,6LAAC;gCAAG,WAAU;0CAAoB;;;;;;;;;;;;kCAKpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,OAAO;oCAAE,YAAY;gCAA4B;;kDAEjD,6LAAC,gIAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;oCACR;;;;;;;0CAGJ,6LAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,OAAO;oCAAE,YAAY;gCAA4B;0CAClD;;;;;;;;;;;;;;;;;;0BAKL,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;kCAGJ,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,KAAI;;0CAEJ,6LAAC,gIAAA,CAAA,UAAK;gCACJ,aAAW;gCACX,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;;;;;;4BACR;;;;;;;;;;;;;;;;;;;AAMZ;GA5GwB;KAAA", "debugId": null}}]}