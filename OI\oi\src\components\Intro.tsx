'use client';

import { useRef, useEffect, useState } from 'react';
import { motion, useScroll, useTransform, useMotionValue, useSpring } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

const Intro = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isAboutHovered, setIsAboutHovered] = useState(false);
  const [isPlaying, setIsPlaying] = useState(true);
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: false
  });

  // Smooth spring animation for the path drawing
  const pathProgress = useSpring(
    useTransform(scrollYProgress, [0, 0.8], [0, 1]),
    { stiffness: 400, damping: 40 }
  );

  // Title animations - smooth sliding effect with mask reveal
  const firstLineY = useTransform(scrollYProgress, [0, 0.3], [100, 0]);
  const firstLineX = useTransform(scrollYProgress, [0.4, 0.7], [0, 200]);
  const firstLineOpacity = useTransform(scrollYProgress, [0.6, 0.8], [1, 0]); // Fade out as it slides right
  
  const secondLineY = useTransform(scrollYProgress, [0.15, 0.45], [-100, 0]); // Slide down from top

  return (
    <section 
      ref={containerRef}
      className="relative min-h-screen bg-gray-50 overflow-hidden py-20"
    >
      {/* Animated Blue Line SVG - Made thicker */}
      <div className="absolute inset-0 pointer-events-none">
        <svg 
          className="absolute inset-0 w-full h-full" 
          viewBox="0 0 1400 800" 
          preserveAspectRatio="none"
        >
          <motion.path
            d="M0,100 Q300,50 600,200 T1200,300 Q1300,350 1400,400"
            stroke="#2563eb"
            strokeWidth="24"
            fill="none"
            strokeLinecap="round"
            pathLength={pathProgress}
            style={{
              pathLength: pathProgress
            }}
          />
        </svg>
      </div>

      <div ref={ref} className="relative z-10 max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-center min-h-screen">
          
          {/* Left Column - Title and Video Player */}
          <motion.div 
            className="relative"
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {/* Animated Title - Two lines with mask reveal */}
            <motion.div className="mb-12 lg:mb-16 relative">
              <motion.h2 
                className="text-6xl lg:text-7xl xl:text-8xl font-medium leading-tight text-black relative"
                style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}
              >
                {/* White mask overlay for reveal effect */}
                <div className="absolute inset-0 bg-gray-50 z-10" 
                     style={{
                       clipPath: `polygon(0 0, ${Math.min(scrollYProgress.get() * 150, 100)}% 0, ${Math.min(scrollYProgress.get() * 150, 100)}% 100%, 0 100%)`
                     }}
                />
                
                
                <motion.div
                  style={{ 
                    y: secondLineY
                  }}
                  className="block relative z-5"
                >
                  Within Reach
                </motion.div>
              </motion.h2>
            </motion.div>

            {/* Video Container with Blue Glass Effect */}
            <motion.div 
              className="relative rounded-3xl overflow-hidden bg-gradient-to-br from-blue-500 to-blue-700 shadow-2xl"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              {/* Video Element */}
              <div className="relative aspect-video">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-600/80 to-blue-800/60 z-10"></div>
                
                {/* Simulated Video Content */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-800">
                  {/* Abstract animated shapes to simulate video content */}
                  <motion.div 
                    className="absolute inset-0 flex items-center justify-center"
                    animate={{
                      background: [
                        "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.8) 0%, transparent 50%)",
                        "radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.8) 0%, transparent 50%)",
                        "radial-gradient(circle at 40% 80%, rgba(29, 78, 216, 0.8) 0%, transparent 50%)",
                        "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.8) 0%, transparent 50%)"
                      ]
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  
                  {/* Floating geometric elements */}
                  <motion.div 
                    className="absolute top-1/4 left-1/4 w-8 h-8 bg-white/30 rounded-lg"
                    animate={{
                      y: [0, -20, 0],
                      rotate: [0, 180, 360]
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  
                  <motion.div 
                    className="absolute top-3/4 right-1/3 w-6 h-6 bg-white/40 rounded-full"
                    animate={{
                      y: [0, 15, 0],
                      x: [0, 10, 0]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  
                  <motion.div 
                    className="absolute top-1/2 right-1/4 w-4 h-12 bg-white/20 rounded-full"
                    animate={{
                      rotate: [0, 360],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                </div>

                {/* Play/Pause Button */}
                <motion.button
                  className="absolute inset-0 z-20 flex items-center justify-center bg-black/10 hover:bg-black/20 transition-colors"
                  onClick={() => setIsPlaying(!isPlaying)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div 
                    className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center"
                    whileHover={{ scale: 1.1 }}
                  >
                    {isPlaying ? (
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                        <rect x="6" y="4" width="4" height="16"/>
                        <rect x="14" y="4" width="4" height="16"/>
                      </svg>
                    ) : (
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                        <polygon points="5,3 19,12 5,21"/>
                      </svg>
                    )}
                  </motion.div>
                </motion.button>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column - Content */}
          <motion.div 
            className="space-y-8"
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {/* Description Text */}
            <motion.p 
              className="text-lg lg:text-xl leading-relaxed text-gray-800 mb-12"
              style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              Lusion is a digital production studio that brings your ideas to life 
              through visually captivating designs and interactive experiences. 
              With our talented team, we push the boundaries by solving 
              complex problems, delivering tailored solutions that exceed 
              expectations and engage audiences.
            </motion.p>

            {/* About Us Button - Enhanced with arrow */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <motion.button
                className="relative bg-white border border-gray-300 rounded-full px-8 py-4 flex items-center gap-3 text-black font-medium text-lg overflow-hidden group"
                style={{ fontFamily: 'Aeonik-Medium, sans-serif', minWidth: '160px' }}
                onHoverStart={() => setIsAboutHovered(true)}
                onHoverEnd={() => setIsAboutHovered(false)}
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                {/* Fast blue fill background */}
                <motion.div
                  className="absolute inset-0 bg-blue-600 rounded-full"
                  initial={{ x: '-100%' }}
                  animate={{
                    x: isAboutHovered ? '0%' : '-100%',
                  }}
                  transition={{ duration: 0.2, ease: "easeOut" }}
                />
                
                {/* Black dot indicator */}
                <motion.div 
                  className="relative z-10 w-2 h-2 bg-black rounded-full"
                  animate={{
                    backgroundColor: isAboutHovered ? "#ffffff" : "#000000"
                  }}
                  transition={{ duration: 0.2 }}
                />
                
                {/* Text with slide left animation */}
                <motion.span 
                  className="relative z-10"
                  animate={{
                    color: isAboutHovered ? "#ffffff" : "#000000",
                    x: isAboutHovered ? -10 : 0
                  }}
                  transition={{ duration: 0.2 }}
                >
                  ABOUT US
                </motion.span>

                {/* Arrow appearing from right */}
                <motion.div
                  className="relative z-10 flex items-center justify-center"
                  initial={{ x: 30, opacity: 0 }}
                  animate={{
                    x: isAboutHovered ? 0 : 30,
                    opacity: isAboutHovered ? 1 : 0
                  }}
                  transition={{ duration: 0.2, ease: "easeOut" }}
                >
                  <svg 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    className="stroke-current"
                    strokeWidth="2"
                  >
                    <motion.path
                      d="M5 12h14M12 5l7 7-7 7"
                      animate={{
                        stroke: isAboutHovered ? "#ffffff" : "#000000"
                      }}
                      transition={{ duration: 0.2 }}
                    />
                  </svg>
                </motion.div>
              </motion.button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Intro;