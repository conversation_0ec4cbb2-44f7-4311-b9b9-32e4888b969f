'use client';

import { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera } from '@react-three/drei';
import { motion } from 'framer-motion';
import * as THREE from 'three';

// Animated 3D Shapes Component
function AnimatedShapes() {
  const groupRef = useRef<THREE.Group>(null);
  const meshRefs = useRef<THREE.Mesh[]>([]);

  // Create multiple geometric shapes
  const shapes = useMemo(() => {
    const shapeTypes = [
      'cylinder',
      'box',
      'torus',
      'sphere',
      'cone'
    ];
    
    return Array.from({ length: 15 }, (_, i) => ({
      type: shapeTypes[i % shapeTypes.length],
      position: [
        (Math.random() - 0.5) * 8,
        (Math.random() - 0.5) * 6,
        (Math.random() - 0.5) * 6
      ] as [number, number, number],
      rotation: [
        Math.random() * Math.PI,
        Math.random() * Math.PI,
        Math.random() * Math.PI
      ] as [number, number, number],
      scale: 0.3 + Math.random() * 0.7,
      color: Math.random() > 0.5 ? '#2563eb' : '#64748b', // Blue or gray
      rotationSpeed: (Math.random() - 0.5) * 0.02
    }));
  }, []);

  useFrame((state) => {
    if (groupRef.current) {
      // Rotate the entire group slowly
      groupRef.current.rotation.y += 0.005;
      
      // Individual mesh rotations
      meshRefs.current.forEach((mesh, i) => {
        if (mesh) {
          mesh.rotation.x += shapes[i].rotationSpeed;
          mesh.rotation.y += shapes[i].rotationSpeed * 0.5;
          mesh.position.y += Math.sin(state.clock.elapsedTime + i) * 0.001;
        }
      });
    }
  });

  const getGeometry = (type: string) => {
    switch (type) {
      case 'cylinder':
        return <cylinderGeometry args={[0.5, 0.5, 1, 8]} />;
      case 'box':
        return <boxGeometry args={[1, 1, 1]} />;
      case 'torus':
        return <torusGeometry args={[0.6, 0.2, 8, 16]} />;
      case 'sphere':
        return <sphereGeometry args={[0.5, 16, 16]} />;
      case 'cone':
        return <coneGeometry args={[0.5, 1, 8]} />;
      default:
        return <boxGeometry args={[1, 1, 1]} />;
    }
  };

  return (
    <group ref={groupRef}>
      {shapes.map((shape, index) => (
        <mesh
          key={index}
          ref={(el) => {
            if (el) meshRefs.current[index] = el;
          }}
          position={shape.position}
          rotation={shape.rotation}
          scale={shape.scale}
        >
          {getGeometry(shape.type)}
          <meshStandardMaterial 
            color={shape.color}
            metalness={0.1}
            roughness={0.2}
          />
        </mesh>
      ))}
    </group>
  );
}

// Environment and Lighting
function Environment() {
  return (
    <>
      <ambientLight intensity={0.3} />
      <directionalLight 
        position={[10, 10, 5]} 
        intensity={1}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <pointLight position={[-10, -10, -10]} intensity={0.5} color="#2563eb" />
    </>
  );
}

const Pool = () => {
  return (
    <motion.div 
      className="w-full h-full relative"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1, delay: 0.5 }}
    >
      <Canvas
        shadows
        className="w-full h-full"
        gl={{ 
          antialias: true,
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        <PerspectiveCamera makeDefault position={[0, 0, 10]} fov={50} />
        
        <Environment />
        <AnimatedShapes />
        
        <OrbitControls
          enablePan={false}
          enableZoom={false}
          enableRotate={true}
          autoRotate={true}
          autoRotateSpeed={0.5}
          maxPolarAngle={Math.PI / 2}
          minPolarAngle={Math.PI / 3}
        />
      </Canvas>
      
      {/* Loading placeholder overlay */}
      <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 text-white pointer-events-none">
        <motion.div
          initial={{ opacity: 1 }}
          animate={{ opacity: 0 }}
          transition={{ duration: 1, delay: 1 }}
          className="text-lg"
          style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}
        >
          Loading 3D Experience...
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Pool;