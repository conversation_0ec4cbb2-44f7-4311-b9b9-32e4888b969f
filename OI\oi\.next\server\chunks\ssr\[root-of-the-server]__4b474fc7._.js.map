{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Loading.tsx"], "sourcesContent": ["// components/Loading.tsx\r\nimport React, { useState, useEffect } from 'react';\r\n\r\ninterface LoadingProps {\r\n  onLoadingComplete: () => void;\r\n}\r\n\r\nconst Loading: React.FC<LoadingProps> = ({ onLoadingComplete }) => {\r\n  const [progress, setProgress] = useState(0);\r\n  const [isComplete, setIsComplete] = useState(false);\r\n  const [isZooming, setIsZooming] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const duration = 3000; // 3 seconds total loading time\r\n    const interval = 30; // Update every 30ms for smoother animation\r\n    const steps = duration / interval;\r\n    const increment = 100 / steps;\r\n\r\n    let currentProgress = 0;\r\n\r\n    const timer = setInterval(() => {\r\n      currentProgress += increment;\r\n      \r\n      if (currentProgress >= 100) {\r\n        clearInterval(timer);\r\n        setProgress(100);\r\n        setIsComplete(true);\r\n        \r\n        // Start zoom animation after progress completes\r\n        setTimeout(() => {\r\n          setIsZooming(true);\r\n        }, 300);\r\n        \r\n        // Call onLoadingComplete after zoom animation\r\n        setTimeout(() => {\r\n          onLoadingComplete();\r\n        }, 1300); // 300ms delay + 1000ms zoom animation\r\n        return;\r\n      }\r\n      \r\n      // Add some easing to make it feel more natural\r\n      const eased = easeOutQuart(currentProgress / 100) * 100;\r\n      setProgress(eased);\r\n    }, interval);\r\n\r\n    return () => clearInterval(timer);\r\n  }, [onLoadingComplete]);\r\n\r\n  // Easing function for smooth animation\r\n  const easeOutQuart = (t: number): number => {\r\n    return 1 - Math.pow(1 - t, 4);\r\n  };\r\n\r\n  return (\r\n    <div className={`fixed inset-0 bg-black flex items-center justify-center z-50 transition-all duration-1000 ${\r\n      isZooming ? 'scale-[50] bg-white' : 'scale-100'\r\n    } ${isComplete && !isZooming ? 'opacity-100' : isComplete ? 'opacity-100' : 'opacity-100'}`}>\r\n      <div className=\"relative w-full max-w-lg px-8\">\r\n        {/* Progress Counter */}\r\n        <div className={`absolute bottom-20 left-8 transition-opacity duration-300 ${\r\n          isZooming ? 'opacity-0' : 'opacity-100'\r\n        }`}>\r\n          <span \r\n            className=\"text-white font-medium tracking-tight select-none\"\r\n            style={{ \r\n              fontFamily: 'Aeonik-Medium, sans-serif',\r\n              fontSize: 'clamp(4rem, 12vw, 8rem)',\r\n              lineHeight: '0.8',\r\n              fontFeatureSettings: '\"tnum\" 1'\r\n            }}\r\n          >\r\n            {Math.floor(progress).toString().padStart(3, '0')}\r\n          </span>\r\n        </div>\r\n\r\n        {/* Progress Bar Container */}\r\n        <div className=\"relative w-full h-0.5 bg-gray-700 mx-auto\">\r\n          {/* Progress Bar Fill */}\r\n          <div \r\n            className={`absolute top-0 left-0 h-full bg-white transition-all duration-75 ease-out ${\r\n              isZooming ? 'scale-y-[2000] origin-center' : ''\r\n            }`}\r\n            style={{ \r\n              width: `${progress}%`,\r\n              transformOrigin: 'left center',\r\n              boxShadow: '0 0 10px rgba(255,255,255,0.3)'\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Loading Text */}\r\n        <div className={`absolute top-20 left-8 transition-opacity duration-300 ${\r\n          isZooming ? 'opacity-0' : 'opacity-100'\r\n        }`}>\r\n          <div \r\n            className=\"text-white text-xs font-normal tracking-widest opacity-50 select-none\"\r\n            style={{ \r\n              fontFamily: 'Aeonik-Regular, sans-serif',\r\n              letterSpacing: '0.2em'\r\n            }}\r\n          >\r\n            LOADING\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Subtle background effect */}\r\n      <div className={`absolute inset-0 overflow-hidden pointer-events-none transition-opacity duration-300 ${\r\n        isZooming ? 'opacity-0' : 'opacity-100'\r\n      }`}>\r\n        <div \r\n          className=\"absolute inset-0 transition-opacity duration-2000\"\r\n          style={{\r\n            background: `linear-gradient(90deg, transparent ${progress - 20}%, rgba(255,255,255,0.03) ${progress}%, transparent ${progress + 20}%)`,\r\n            opacity: progress > 10 ? 1 : 0\r\n          }}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Loading;"], "names": [], "mappings": "AAAA,yBAAyB;;;;;AACzB;;;AAMA,MAAM,UAAkC,CAAC,EAAE,iBAAiB,EAAE;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,MAAM,+BAA+B;QACtD,MAAM,WAAW,IAAI,2CAA2C;QAChE,MAAM,QAAQ,WAAW;QACzB,MAAM,YAAY,MAAM;QAExB,IAAI,kBAAkB;QAEtB,MAAM,QAAQ,YAAY;YACxB,mBAAmB;YAEnB,IAAI,mBAAmB,KAAK;gBAC1B,cAAc;gBACd,YAAY;gBACZ,cAAc;gBAEd,gDAAgD;gBAChD,WAAW;oBACT,aAAa;gBACf,GAAG;gBAEH,8CAA8C;gBAC9C,WAAW;oBACT;gBACF,GAAG,OAAO,sCAAsC;gBAChD;YACF;YAEA,+CAA+C;YAC/C,MAAM,QAAQ,aAAa,kBAAkB,OAAO;YACpD,YAAY;QACd,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAkB;IAEtB,uCAAuC;IACvC,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,0FAA0F,EACzG,YAAY,wBAAwB,YACrC,CAAC,EAAE,cAAc,CAAC,YAAY,gBAAgB,aAAa,gBAAgB,eAAe;;0BACzF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAW,CAAC,0DAA0D,EACzE,YAAY,cAAc,eAC1B;kCACA,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,UAAU;gCACV,YAAY;gCACZ,qBAAqB;4BACvB;sCAEC,KAAK,KAAK,CAAC,UAAU,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;kCAKjD,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BACC,WAAW,CAAC,0EAA0E,EACpF,YAAY,iCAAiC,IAC7C;4BACF,OAAO;gCACL,OAAO,GAAG,SAAS,CAAC,CAAC;gCACrB,iBAAiB;gCACjB,WAAW;4BACb;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAW,CAAC,uDAAuD,EACtE,YAAY,cAAc,eAC1B;kCACA,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,eAAe;4BACjB;sCACD;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAW,CAAC,qFAAqF,EACpG,YAAY,cAAc,eAC1B;0BACA,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY,CAAC,mCAAmC,EAAE,WAAW,GAAG,0BAA0B,EAAE,SAAS,eAAe,EAAE,WAAW,GAAG,EAAE,CAAC;wBACvI,SAAS,WAAW,KAAK,IAAI;oBAC/B;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Pool.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useRef, useMemo } from 'react';\r\nimport { Canvas, useFrame } from '@react-three/fiber';\r\nimport { OrbitControls, PerspectiveCamera } from '@react-three/drei';\r\nimport { motion } from 'framer-motion';\r\nimport * as THREE from 'three';\r\n\r\n// Animated 3D Shapes Component\r\nfunction AnimatedShapes() {\r\n  const groupRef = useRef<THREE.Group>(null);\r\n  const meshRefs = useRef<THREE.Mesh[]>([]);\r\n\r\n  // Create multiple geometric shapes\r\n  const shapes = useMemo(() => {\r\n    const shapeTypes = [\r\n      'cylinder',\r\n      'box',\r\n      'torus',\r\n      'sphere',\r\n      'cone'\r\n    ];\r\n    \r\n    return Array.from({ length: 15 }, (_, i) => ({\r\n      type: shapeTypes[i % shapeTypes.length],\r\n      position: [\r\n        (Math.random() - 0.5) * 8,\r\n        (Math.random() - 0.5) * 6,\r\n        (Math.random() - 0.5) * 6\r\n      ] as [number, number, number],\r\n      rotation: [\r\n        Math.random() * Math.PI,\r\n        Math.random() * Math.PI,\r\n        Math.random() * Math.PI\r\n      ] as [number, number, number],\r\n      scale: 0.3 + Math.random() * 0.7,\r\n      color: Math.random() > 0.5 ? '#2563eb' : '#64748b', // Blue or gray\r\n      rotationSpeed: (Math.random() - 0.5) * 0.02\r\n    }));\r\n  }, []);\r\n\r\n  useFrame((state) => {\r\n    if (groupRef.current) {\r\n      // Rotate the entire group slowly\r\n      groupRef.current.rotation.y += 0.005;\r\n      \r\n      // Individual mesh rotations\r\n      meshRefs.current.forEach((mesh, i) => {\r\n        if (mesh) {\r\n          mesh.rotation.x += shapes[i].rotationSpeed;\r\n          mesh.rotation.y += shapes[i].rotationSpeed * 0.5;\r\n          mesh.position.y += Math.sin(state.clock.elapsedTime + i) * 0.001;\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  const getGeometry = (type: string) => {\r\n    switch (type) {\r\n      case 'cylinder':\r\n        return <cylinderGeometry args={[0.5, 0.5, 1, 8]} />;\r\n      case 'box':\r\n        return <boxGeometry args={[1, 1, 1]} />;\r\n      case 'torus':\r\n        return <torusGeometry args={[0.6, 0.2, 8, 16]} />;\r\n      case 'sphere':\r\n        return <sphereGeometry args={[0.5, 16, 16]} />;\r\n      case 'cone':\r\n        return <coneGeometry args={[0.5, 1, 8]} />;\r\n      default:\r\n        return <boxGeometry args={[1, 1, 1]} />;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <group ref={groupRef}>\r\n      {shapes.map((shape, index) => (\r\n        <mesh\r\n          key={index}\r\n          ref={(el) => {\r\n            if (el) meshRefs.current[index] = el;\r\n          }}\r\n          position={shape.position}\r\n          rotation={shape.rotation}\r\n          scale={shape.scale}\r\n        >\r\n          {getGeometry(shape.type)}\r\n          <meshStandardMaterial \r\n            color={shape.color}\r\n            metalness={0.1}\r\n            roughness={0.2}\r\n          />\r\n        </mesh>\r\n      ))}\r\n    </group>\r\n  );\r\n}\r\n\r\n// Environment and Lighting\r\nfunction Environment() {\r\n  return (\r\n    <>\r\n      <ambientLight intensity={0.3} />\r\n      <directionalLight \r\n        position={[10, 10, 5]} \r\n        intensity={1}\r\n        castShadow\r\n        shadow-mapSize-width={2048}\r\n        shadow-mapSize-height={2048}\r\n      />\r\n      <pointLight position={[-10, -10, -10]} intensity={0.5} color=\"#2563eb\" />\r\n    </>\r\n  );\r\n}\r\n\r\nconst Pool = () => {\r\n  return (\r\n    <motion.div \r\n      className=\"w-full h-full relative\"\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      transition={{ duration: 1, delay: 0.5 }}\r\n    >\r\n      <Canvas\r\n        shadows\r\n        className=\"w-full h-full\"\r\n        gl={{ \r\n          antialias: true,\r\n          alpha: true,\r\n          powerPreference: \"high-performance\"\r\n        }}\r\n      >\r\n        <PerspectiveCamera makeDefault position={[0, 0, 10]} fov={50} />\r\n        \r\n        <Environment />\r\n        <AnimatedShapes />\r\n        \r\n        <OrbitControls\r\n          enablePan={false}\r\n          enableZoom={false}\r\n          enableRotate={true}\r\n          autoRotate={true}\r\n          autoRotateSpeed={0.5}\r\n          maxPolarAngle={Math.PI / 2}\r\n          minPolarAngle={Math.PI / 3}\r\n        />\r\n      </Canvas>\r\n      \r\n      {/* Loading placeholder overlay */}\r\n      <div className=\"absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 text-white pointer-events-none\">\r\n        <motion.div\r\n          initial={{ opacity: 1 }}\r\n          animate={{ opacity: 0 }}\r\n          transition={{ duration: 1, delay: 1 }}\r\n          className=\"text-lg\"\r\n          style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}\r\n        >\r\n          Loading 3D Experience...\r\n        </motion.div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default Pool;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AALA;;;;;;AAQA,+BAA+B;AAC/B,SAAS;IACP,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACrC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB,EAAE;IAExC,mCAAmC;IACnC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACrB,MAAM,aAAa;YACjB;YACA;YACA;YACA;YACA;SACD;QAED,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAG,GAAG,CAAC,GAAG,IAAM,CAAC;gBAC3C,MAAM,UAAU,CAAC,IAAI,WAAW,MAAM,CAAC;gBACvC,UAAU;oBACR,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;iBACzB;gBACD,UAAU;oBACR,KAAK,MAAM,KAAK,KAAK,EAAE;oBACvB,KAAK,MAAM,KAAK,KAAK,EAAE;oBACvB,KAAK,MAAM,KAAK,KAAK,EAAE;iBACxB;gBACD,OAAO,MAAM,KAAK,MAAM,KAAK;gBAC7B,OAAO,KAAK,MAAM,KAAK,MAAM,YAAY;gBACzC,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACzC,CAAC;IACH,GAAG,EAAE;IAEL,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,SAAS,OAAO,EAAE;YACpB,iCAAiC;YACjC,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;YAE/B,4BAA4B;YAC5B,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM;gBAC9B,IAAI,MAAM;oBACR,KAAK,QAAQ,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,aAAa;oBAC1C,KAAK,QAAQ,CAAC,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,aAAa,GAAG;oBAC7C,KAAK,QAAQ,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,KAAK;gBAC7D;YACF;QACF;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAiB,MAAM;wBAAC;wBAAK;wBAAK;wBAAG;qBAAE;;;;;;YACjD,KAAK;gBACH,qBAAO,8OAAC;oBAAY,MAAM;wBAAC;wBAAG;wBAAG;qBAAE;;;;;;YACrC,KAAK;gBACH,qBAAO,8OAAC;oBAAc,MAAM;wBAAC;wBAAK;wBAAK;wBAAG;qBAAG;;;;;;YAC/C,KAAK;gBACH,qBAAO,8OAAC;oBAAe,MAAM;wBAAC;wBAAK;wBAAI;qBAAG;;;;;;YAC5C,KAAK;gBACH,qBAAO,8OAAC;oBAAa,MAAM;wBAAC;wBAAK;wBAAG;qBAAE;;;;;;YACxC;gBACE,qBAAO,8OAAC;oBAAY,MAAM;wBAAC;wBAAG;wBAAG;qBAAE;;;;;;QACvC;IACF;IAEA,qBACE,8OAAC;QAAM,KAAK;kBACT,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;gBAEC,KAAK,CAAC;oBACJ,IAAI,IAAI,SAAS,OAAO,CAAC,MAAM,GAAG;gBACpC;gBACA,UAAU,MAAM,QAAQ;gBACxB,UAAU,MAAM,QAAQ;gBACxB,OAAO,MAAM,KAAK;;oBAEjB,YAAY,MAAM,IAAI;kCACvB,8OAAC;wBACC,OAAO,MAAM,KAAK;wBAClB,WAAW;wBACX,WAAW;;;;;;;eAZR;;;;;;;;;;AAkBf;AAEA,2BAA2B;AAC3B,SAAS;IACP,qBACE;;0BACE,8OAAC;gBAAa,WAAW;;;;;;0BACzB,8OAAC;gBACC,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBACrB,WAAW;gBACX,UAAU;gBACV,wBAAsB;gBACtB,yBAAuB;;;;;;0BAEzB,8OAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAI,CAAC;oBAAI,CAAC;iBAAG;gBAAE,WAAW;gBAAK,OAAM;;;;;;;;AAGnE;AAEA,MAAM,OAAO;IACX,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;YAAG,OAAO;QAAI;;0BAEtC,8OAAC,mMAAA,CAAA,SAAM;gBACL,OAAO;gBACP,WAAU;gBACV,IAAI;oBACF,WAAW;oBACX,OAAO;oBACP,iBAAiB;gBACnB;;kCAEA,8OAAC,qKAAA,CAAA,oBAAiB;wBAAC,WAAW;wBAAC,UAAU;4BAAC;4BAAG;4BAAG;yBAAG;wBAAE,KAAK;;;;;;kCAE1D,8OAAC;;;;;kCACD,8OAAC;;;;;kCAED,8OAAC,iKAAA,CAAA,gBAAa;wBACZ,WAAW;wBACX,YAAY;wBACZ,cAAc;wBACd,YAAY;wBACZ,iBAAiB;wBACjB,eAAe,KAAK,EAAE,GAAG;wBACzB,eAAe,KAAK,EAAE,GAAG;;;;;;;;;;;;0BAK7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;wBAAG,OAAO;oBAAE;oBACpC,WAAU;oBACV,OAAO;wBAAE,YAAY;oBAA6B;8BACnD;;;;;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Hero.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport Pool from './Pool';\r\n\r\nconst Hero = () => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isLetsTalkHovered, setIsLetsTalkHovered] = useState(false);\r\n  const [isMenuHovered, setIsMenuHovered] = useState(false);\r\n  const [isSoundOn, setIsSoundOn] = useState(true);\r\n\r\n  return (\r\n    <div className=\"relative min-h-screen bg-gray-50 overflow-hidden\">\r\n      {/* Navigation */}\r\n      <nav className=\"relative z-50 flex items-start justify-between p-6 lg:p-8\">\r\n        {/* Left side - LUSION and Arrow */}\r\n        <div className=\"flex items-center gap-4\">\r\n          <h1 className=\"text-2xl lg:text-3xl font-medium text-black\" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>\r\n            LUSION\r\n          </h1>\r\n          {/* Arrow Icon */}\r\n          <motion.div \r\n            className=\"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center cursor-pointer\"\r\n            whileHover={{ scale: 1.1 }}\r\n            transition={{ duration: 0.2 }}\r\n          >\r\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n              <path d=\"M7 17L17 7M17 7H7M17 7V17\"/>\r\n            </svg>\r\n          </motion.div>\r\n        </div>\r\n\r\n        {/* Center - Hero Text aligned with navigation */}\r\n        <div className=\"flex-1 max-w-xl mx-8\">\r\n          <motion.h2 \r\n            className=\"text-4xl lg:text-4xl xl:text-4xl leading-tight text-black\"\r\n            style={{ fontFamily: 'Aeonik-Mediem, sans-serif' }}\r\n            initial={{ opacity: 0, y: 50 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n          >\r\n            We help brands create digital experiences that connect with their audience\r\n          </motion.h2>\r\n        </div>\r\n\r\n        {/* Right side - Buttons */}\r\n        <div className=\"flex items-center gap-4\">\r\n          {/* Sound Switch Button */}\r\n          <motion.button\r\n            className=\"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center cursor-pointer\"\r\n            onClick={() => setIsSoundOn(!isSoundOn)}\r\n            whileHover={{ scale: 1.1 }}\r\n            transition={{ duration: 0.2 }}\r\n          >\r\n            {isSoundOn ? (\r\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                <polygon points=\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\"></polygon>\r\n                <path d=\"M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07\"></path>\r\n              </svg>\r\n            ) : (\r\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                <polygon points=\"11 5 6 9 2 9 2 15 6 15 11 19 11 5\"></polygon>\r\n                <line x1=\"23\" y1=\"9\" x2=\"17\" y2=\"15\"></line>\r\n                <line x1=\"17\" y1=\"9\" x2=\"23\" y2=\"15\"></line>\r\n              </svg>\r\n            )}\r\n          </motion.button>\r\n\r\n          {/* Let's Talk Button */}\r\n          <motion.button\r\n            className=\"relative bg-gray-800 text-white px-6 py-3 rounded-full flex items-center gap-3 text-sm font-medium overflow-hidden\"\r\n            style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}\r\n            onHoverStart={() => setIsLetsTalkHovered(true)}\r\n            onHoverEnd={() => setIsLetsTalkHovered(false)}\r\n            whileHover={{ scale: 1.02 }}\r\n            transition={{ duration: 0.3, ease: \"easeOut\" }}\r\n          >\r\n            {/* Blue background slide */}\r\n            <motion.div\r\n              className=\"absolute inset-0 bg-blue-600\"\r\n              initial={{ x: \"-100%\" }}\r\n              animate={{ x: isLetsTalkHovered ? \"0%\" : \"-100%\" }}\r\n              transition={{ duration: 0.4, ease: \"easeInOut\" }}\r\n            />\r\n            \r\n            {/* Arrow from left */}\r\n            <motion.div\r\n              className=\"relative z-10\"\r\n              initial={{ x: -20, opacity: 0 }}\r\n              animate={{ \r\n                x: isLetsTalkHovered ? 0 : -20, \r\n                opacity: isLetsTalkHovered ? 1 : 0 \r\n              }}\r\n              transition={{ duration: 0.3, ease: \"easeOut\" }}\r\n            >\r\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                <path d=\"M5 12h14M12 5l7 7-7 7\"/>\r\n              </svg>\r\n            </motion.div>\r\n            \r\n            <span className=\"relative z-10\">LET'S TALK</span>\r\n            \r\n            <motion.div\r\n              className=\"relative z-10 w-2 h-2 bg-white rounded-full\"\r\n              animate={{ scale: isLetsTalkHovered ? 1.2 : 1 }}\r\n              transition={{ duration: 0.3 }}\r\n            />\r\n          </motion.button>\r\n\r\n          {/* Menu Button */}\r\n          <motion.button \r\n            className=\"bg-gray-200 hover:bg-gray-300 text-black px-6 py-3 rounded-full text-sm font-medium transition-colors duration-300 overflow-hidden relative\"\r\n            style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}\r\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\r\n            onHoverStart={() => setIsMenuHovered(true)}\r\n            onHoverEnd={() => setIsMenuHovered(false)}\r\n          >\r\n            <motion.span\r\n              animate={{ y: isMenuHovered ? -30 : 0 }}\r\n              transition={{ duration: 0.3, ease: \"easeInOut\" }}\r\n              className=\"block\"\r\n            >\r\n              MENU ••\r\n            </motion.span>\r\n            <motion.span\r\n              className=\"absolute inset-0 flex items-center justify-center\"\r\n              animate={{ y: isMenuHovered ? 0 : 30 }}\r\n              transition={{ duration: 0.3, ease: \"easeInOut\" }}\r\n            >\r\n              CLOSE ⋮\r\n            </motion.span>\r\n          </motion.button>\r\n        </div>\r\n      </nav>\r\n\r\n      {/* Menu Overlay */}\r\n      <AnimatePresence>\r\n        {isMenuOpen && (\r\n          <motion.div\r\n            className=\"fixed inset-0 z-40 bg-white bg-opacity-95 backdrop-blur-sm\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            transition={{ duration: 0.3 }}\r\n          >\r\n            <div className=\"flex flex-col justify-center items-start px-6 lg:px-8 h-full\">\r\n              {/* Navigation Menu */}\r\n              <motion.div\r\n                className=\"bg-white rounded-3xl p-8 shadow-lg mb-8 w-full max-w-md\"\r\n                initial={{ opacity: 0, y: 50 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                exit={{ opacity: 0, y: 50 }}\r\n                transition={{ duration: 0.4, delay: 0.1 }}\r\n              >\r\n                <nav className=\"space-y-8\">\r\n                  <motion.div \r\n                    className=\"flex items-center justify-between\"\r\n                    whileHover={{ x: 10 }}\r\n                    transition={{ duration: 0.2 }}\r\n                  >\r\n                    <span className=\"text-2xl font-medium\" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>\r\n                      HOME\r\n                    </span>\r\n                    <div className=\"w-2 h-2 bg-black rounded-full\"></div>\r\n                  </motion.div>\r\n                  \r\n                  <motion.div \r\n                    className=\"text-2xl font-medium cursor-pointer\"\r\n                    style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}\r\n                    whileHover={{ x: 10 }}\r\n                    transition={{ duration: 0.2 }}\r\n                  >\r\n                    ABOUT US\r\n                  </motion.div>\r\n                  \r\n                  <motion.div \r\n                    className=\"text-2xl font-medium cursor-pointer\"\r\n                    style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}\r\n                    whileHover={{ x: 10 }}\r\n                    transition={{ duration: 0.2 }}\r\n                  >\r\n                    PROJECTS\r\n                  </motion.div>\r\n                  \r\n                  <motion.div \r\n                    className=\"text-2xl font-medium cursor-pointer\"\r\n                    style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}\r\n                    whileHover={{ x: 10 }}\r\n                    transition={{ duration: 0.2 }}\r\n                  >\r\n                    CONTACT\r\n                  </motion.div>\r\n                </nav>\r\n              </motion.div>\r\n\r\n              {/* Newsletter Subscription */}\r\n              <motion.div\r\n                className=\"bg-white rounded-3xl p-8 shadow-lg w-full max-w-md\"\r\n                initial={{ opacity: 0, y: 50 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                exit={{ opacity: 0, y: 50 }}\r\n                transition={{ duration: 0.4, delay: 0.2 }}\r\n              >\r\n                <h3 className=\"text-3xl font-medium mb-6\" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>\r\n                  Subscribe to our newsletter\r\n                </h3>\r\n                \r\n                <div className=\"relative\">\r\n                  <input\r\n                    type=\"email\"\r\n                    placeholder=\"Your email\"\r\n                    className=\"w-full bg-gray-100 rounded-full px-6 py-4 pr-16 text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                    style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}\r\n                  />\r\n                  <motion.button\r\n                    className=\"absolute right-2 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black rounded-full flex items-center justify-center text-white\"\r\n                    whileHover={{ scale: 1.1 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                  >\r\n                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                      <path d=\"M5 12h14M12 5l7 7-7 7\"/>\r\n                    </svg>\r\n                  </motion.button>\r\n                </div>\r\n              </motion.div>\r\n\r\n              {/* Footer Section */}\r\n              <motion.div\r\n                className=\"bg-black rounded-t-3xl p-8 w-full max-w-md mt-8\"\r\n                initial={{ opacity: 0, y: 50 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                exit={{ opacity: 0, y: 50 }}\r\n                transition={{ duration: 0.4, delay: 0.3 }}\r\n              >\r\n                <div className=\"flex items-center justify-between text-white\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <div className=\"w-3 h-3 bg-white rounded-full\"></div>\r\n                    <div className=\"w-3 h-3 bg-white rounded-full\"></div>\r\n                    <span className=\"text-xl font-medium ml-4\" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>\r\n                      LABS\r\n                    </span>\r\n                  </div>\r\n                  <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\">\r\n                    <path d=\"M7 17L17 7M17 7H7M17 7V17\"/>\r\n                  </svg>\r\n                </div>\r\n              </motion.div>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* 3D Pool Section - Moved down, full width */}\r\n      <div className=\"relative z-10 px-6 lg:px-8 pt-16 pb-24\">\r\n        <motion.div \r\n          className=\"relative w-full h-[300px] lg:h-[500px] bg-gray-900 rounded-3xl overflow-hidden\"\r\n          initial={{ opacity: 0, scale: 0.8 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          transition={{ duration: 0.8, delay: 0.4 }}\r\n        >\r\n          <Pool />\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Bottom Navigation Dots */}\r\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\">\r\n        <motion.div \r\n          className=\"text-sm text-gray-600 tracking-wider\"\r\n          style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ duration: 0.8, delay: 0.6 }}\r\n        >\r\n          SCROLL TO EXPLORE\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Corner Plus Icons */}\r\n      <div className=\"absolute bottom-16 left-8 z-20\">\r\n        <motion.div \r\n          className=\"w-8 h-8 flex items-center justify-center text-2xl text-gray-400 cursor-pointer hover:text-black transition-colors\"\r\n          whileHover={{ rotate: 90 }}\r\n          transition={{ duration: 0.3 }}\r\n        >\r\n          +\r\n        </motion.div>\r\n      </div>\r\n\r\n      <div className=\"absolute bottom-16 left-1/3 transform -translate-x-1/2 z-20\">\r\n        <motion.div \r\n          className=\"w-8 h-8 flex items-center justify-center text-2xl text-gray-400 cursor-pointer hover:text-black transition-colors\"\r\n          whileHover={{ rotate: 90 }}\r\n          transition={{ duration: 0.3 }}\r\n        >\r\n          +\r\n        </motion.div>\r\n      </div>\r\n\r\n      <div className=\"absolute bottom-16 right-1/3 transform translate-x-1/2 z-20\">\r\n        <motion.div \r\n          className=\"w-8 h-8 flex items-center justify-center text-2xl text-gray-400 cursor-pointer hover:text-black transition-colors\"\r\n          whileHover={{ rotate: 90 }}\r\n          transition={{ duration: 0.3 }}\r\n        >\r\n          +\r\n        </motion.div>\r\n      </div>\r\n\r\n      <div className=\"absolute bottom-16 right-8 z-20\">\r\n        <motion.div \r\n          className=\"w-8 h-8 flex items-center justify-center text-2xl text-gray-400 cursor-pointer hover:text-black transition-colors\"\r\n          whileHover={{ rotate: 90 }}\r\n          transition={{ duration: 0.3 }}\r\n        >\r\n          +\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Hero;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,OAAO;IACX,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;gCAA8C,OAAO;oCAAE,YAAY;gCAA4B;0CAAG;;;;;;0CAIhH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAI;gCACzB,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;oCAAO,QAAO;oCAAe,aAAY;8CAC5F,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAMd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,OAAO;gCAAE,YAAY;4BAA4B;4BACjD,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCACzC;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS,IAAM,aAAa,CAAC;gCAC7B,YAAY;oCAAE,OAAO;gCAAI;gCACzB,YAAY;oCAAE,UAAU;gCAAI;0CAE3B,0BACC,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;oCAAO,QAAO;oCAAe,aAAY;;sDAC5F,8OAAC;4CAAQ,QAAO;;;;;;sDAChB,8OAAC;4CAAK,GAAE;;;;;;;;;;;6FAGV,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;oCAAO,QAAO;oCAAe,aAAY;;sDAC5F,8OAAC;4CAAQ,QAAO;;;;;;sDAChB,8OAAC;4CAAK,IAAG;4CAAK,IAAG;4CAAI,IAAG;4CAAK,IAAG;;;;;;sDAChC,8OAAC;4CAAK,IAAG;4CAAK,IAAG;4CAAI,IAAG;4CAAK,IAAG;;;;;;;;;;;;;;;;;0CAMtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,OAAO;oCAAE,YAAY;gCAA4B;gCACjD,cAAc,IAAM,qBAAqB;gCACzC,YAAY,IAAM,qBAAqB;gCACvC,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAU;;kDAG7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,GAAG;wCAAQ;wCACtB,SAAS;4CAAE,GAAG,oBAAoB,OAAO;wCAAQ;wCACjD,YAAY;4CAAE,UAAU;4CAAK,MAAM;wCAAY;;;;;;kDAIjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,GAAG,CAAC;4CAAI,SAAS;wCAAE;wCAC9B,SAAS;4CACP,GAAG,oBAAoB,IAAI,CAAC;4CAC5B,SAAS,oBAAoB,IAAI;wCACnC;wCACA,YAAY;4CAAE,UAAU;4CAAK,MAAM;wCAAU;kDAE7C,cAAA,8OAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;4CAAO,QAAO;4CAAe,aAAY;sDAC5F,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAIZ,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAEhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,OAAO,oBAAoB,MAAM;wCAAE;wCAC9C,YAAY;4CAAE,UAAU;wCAAI;;;;;;;;;;;;0CAKhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,OAAO;oCAAE,YAAY;gCAA4B;gCACjD,SAAS,IAAM,cAAc,CAAC;gCAC9B,cAAc,IAAM,iBAAiB;gCACrC,YAAY,IAAM,iBAAiB;;kDAEnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,SAAS;4CAAE,GAAG,gBAAgB,CAAC,KAAK;wCAAE;wCACtC,YAAY;4CAAE,UAAU;4CAAK,MAAM;wCAAY;wCAC/C,WAAU;kDACX;;;;;;kDAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,WAAU;wCACV,SAAS;4CAAE,GAAG,gBAAgB,IAAI;wCAAG;wCACrC,YAAY;4CAAE,UAAU;4CAAK,MAAM;wCAAY;kDAChD;;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC,yLAAA,CAAA,kBAAe;0BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC1B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CAExC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,GAAG;4CAAG;4CACpB,YAAY;gDAAE,UAAU;4CAAI;;8DAE5B,8OAAC;oDAAK,WAAU;oDAAuB,OAAO;wDAAE,YAAY;oDAA4B;8DAAG;;;;;;8DAG3F,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAGjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,OAAO;gDAAE,YAAY;4CAA4B;4CACjD,YAAY;gDAAE,GAAG;4CAAG;4CACpB,YAAY;gDAAE,UAAU;4CAAI;sDAC7B;;;;;;sDAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,OAAO;gDAAE,YAAY;4CAA4B;4CACjD,YAAY;gDAAE,GAAG;4CAAG;4CACpB,YAAY;gDAAE,UAAU;4CAAI;sDAC7B;;;;;;sDAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,OAAO;gDAAE,YAAY;4CAA4B;4CACjD,YAAY;gDAAE,GAAG;4CAAG;4CACpB,YAAY;gDAAE,UAAU;4CAAI;sDAC7B;;;;;;;;;;;;;;;;;0CAOL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC1B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAG,WAAU;wCAA4B,OAAO;4CAAE,YAAY;wCAA4B;kDAAG;;;;;;kDAI9F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;gDACV,OAAO;oDAAE,YAAY;gDAA6B;;;;;;0DAEpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAK;0DAExB,cAAA,8OAAC;oDAAI,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;oDAAO,QAAO;oDAAe,aAAY;8DAC5F,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC1B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CAExC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;oDAA2B,OAAO;wDAAE,YAAY;oDAA4B;8DAAG;;;;;;;;;;;;sDAIjG,8OAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;4CAAO,QAAO;4CAAe,aAAY;sDAC5F,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC,0HAAA,CAAA,UAAI;;;;;;;;;;;;;;;0BAKT,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBAAE,YAAY;oBAA6B;oBAClD,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BACzC;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,YAAY;wBAAE,QAAQ;oBAAG;oBACzB,YAAY;wBAAE,UAAU;oBAAI;8BAC7B;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,YAAY;wBAAE,QAAQ;oBAAG;oBACzB,YAAY;wBAAE,UAAU;oBAAI;8BAC7B;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,YAAY;wBAAE,QAAQ;oBAAG;oBACzB,YAAY;wBAAE,UAAU;oBAAI;8BAC7B;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,YAAY;wBAAE,QAAQ;oBAAG;oBACzB,YAAY;wBAAE,UAAU;oBAAI;8BAC7B;;;;;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Intro.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useRef, useEffect, useState } from 'react';\r\nimport { motion, useScroll, useTransform, useMotionValue, useSpring } from 'framer-motion';\r\nimport { useInView } from 'react-intersection-observer';\r\n\r\nconst Intro = () => {\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const [isAboutHovered, setIsAboutHovered] = useState(false);\r\n  const [isPlaying, setIsPlaying] = useState(true);\r\n  \r\n  const { scrollYProgress } = useScroll({\r\n    target: containerRef,\r\n    offset: [\"start end\", \"end start\"]\r\n  });\r\n\r\n  const [ref, inView] = useInView({\r\n    threshold: 0.1,\r\n    triggerOnce: false\r\n  });\r\n\r\n  // Smooth spring animation for the path drawing\r\n  const pathProgress = useSpring(\r\n    useTransform(scrollYProgress, [0, 0.8], [0, 1]),\r\n    { stiffness: 400, damping: 40 }\r\n  );\r\n\r\n  // Title animations - smooth sliding effect with mask reveal\r\n  const firstLineY = useTransform(scrollYProgress, [0, 0.3], [100, 0]);\r\n  const firstLineX = useTransform(scrollYProgress, [0.4, 0.7], [0, 200]);\r\n  const firstLineOpacity = useTransform(scrollYProgress, [0.6, 0.8], [1, 0]); // Fade out as it slides right\r\n  \r\n  const secondLineY = useTransform(scrollYProgress, [0.15, 0.45], [-100, 0]); // Slide down from top\r\n\r\n  return (\r\n    <section \r\n      ref={containerRef}\r\n      className=\"relative min-h-screen bg-gray-50 overflow-hidden py-20\"\r\n    >\r\n      {/* Animated Blue Line SVG - Made thicker */}\r\n      <div className=\"absolute inset-0 pointer-events-none\">\r\n        <svg \r\n          className=\"absolute inset-0 w-full h-full\" \r\n          viewBox=\"0 0 1400 800\" \r\n          preserveAspectRatio=\"none\"\r\n        >\r\n          <motion.path\r\n            d=\"M0,100 Q300,50 600,200 T1200,300 Q1300,350 1400,400\"\r\n            stroke=\"#2563eb\"\r\n            strokeWidth=\"24\"\r\n            fill=\"none\"\r\n            strokeLinecap=\"round\"\r\n            pathLength={pathProgress}\r\n            style={{\r\n              pathLength: pathProgress\r\n            }}\r\n          />\r\n        </svg>\r\n      </div>\r\n\r\n      <div ref={ref} className=\"relative z-10 max-w-7xl mx-auto px-6 lg:px-8\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-center min-h-screen\">\r\n          \r\n          {/* Left Column - Title and Video Player */}\r\n          <motion.div \r\n            className=\"relative\"\r\n            initial={{ opacity: 0, x: -50 }}\r\n            animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n          >\r\n            {/* Animated Title - Two lines with mask reveal */}\r\n            <motion.div className=\"mb-12 lg:mb-16 relative\">\r\n              <motion.h2 \r\n                className=\"text-6xl lg:text-7xl xl:text-8xl font-medium leading-tight text-black relative\"\r\n                style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}\r\n              >\r\n                {/* White mask overlay for reveal effect */}\r\n                <div className=\"absolute inset-0 bg-gray-50 z-10\" \r\n                     style={{\r\n                       clipPath: `polygon(0 0, ${Math.min(scrollYProgress.get() * 150, 100)}% 0, ${Math.min(scrollYProgress.get() * 150, 100)}% 100%, 0 100%)`\r\n                     }}\r\n                />\r\n                \r\n                \r\n                <motion.div\r\n                  style={{ \r\n                    y: secondLineY\r\n                  }}\r\n                  className=\"block relative z-5\"\r\n                >\r\n                  Within Reach\r\n                </motion.div>\r\n              </motion.h2>\r\n            </motion.div>\r\n\r\n            {/* Video Container with Blue Glass Effect */}\r\n            <motion.div \r\n              className=\"relative rounded-3xl overflow-hidden bg-gradient-to-br from-blue-500 to-blue-700 shadow-2xl\"\r\n              whileHover={{ scale: 1.02 }}\r\n              transition={{ duration: 0.3 }}\r\n            >\r\n              {/* Video Element */}\r\n              <div className=\"relative aspect-video\">\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-600/80 to-blue-800/60 z-10\"></div>\r\n                \r\n                {/* Simulated Video Content */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-800\">\r\n                  {/* Abstract animated shapes to simulate video content */}\r\n                  <motion.div \r\n                    className=\"absolute inset-0 flex items-center justify-center\"\r\n                    animate={{\r\n                      background: [\r\n                        \"radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.8) 0%, transparent 50%)\",\r\n                        \"radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.8) 0%, transparent 50%)\",\r\n                        \"radial-gradient(circle at 40% 80%, rgba(29, 78, 216, 0.8) 0%, transparent 50%)\",\r\n                        \"radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.8) 0%, transparent 50%)\"\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 8,\r\n                      repeat: Infinity,\r\n                      ease: \"easeInOut\"\r\n                    }}\r\n                  />\r\n                  \r\n                  {/* Floating geometric elements */}\r\n                  <motion.div \r\n                    className=\"absolute top-1/4 left-1/4 w-8 h-8 bg-white/30 rounded-lg\"\r\n                    animate={{\r\n                      y: [0, -20, 0],\r\n                      rotate: [0, 180, 360]\r\n                    }}\r\n                    transition={{\r\n                      duration: 6,\r\n                      repeat: Infinity,\r\n                      ease: \"easeInOut\"\r\n                    }}\r\n                  />\r\n                  \r\n                  <motion.div \r\n                    className=\"absolute top-3/4 right-1/3 w-6 h-6 bg-white/40 rounded-full\"\r\n                    animate={{\r\n                      y: [0, 15, 0],\r\n                      x: [0, 10, 0]\r\n                    }}\r\n                    transition={{\r\n                      duration: 4,\r\n                      repeat: Infinity,\r\n                      ease: \"easeInOut\"\r\n                    }}\r\n                  />\r\n                  \r\n                  <motion.div \r\n                    className=\"absolute top-1/2 right-1/4 w-4 h-12 bg-white/20 rounded-full\"\r\n                    animate={{\r\n                      rotate: [0, 360],\r\n                      scale: [1, 1.2, 1]\r\n                    }}\r\n                    transition={{\r\n                      duration: 8,\r\n                      repeat: Infinity,\r\n                      ease: \"easeInOut\"\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Play/Pause Button */}\r\n                <motion.button\r\n                  className=\"absolute inset-0 z-20 flex items-center justify-center bg-black/10 hover:bg-black/20 transition-colors\"\r\n                  onClick={() => setIsPlaying(!isPlaying)}\r\n                  whileHover={{ scale: 1.05 }}\r\n                  whileTap={{ scale: 0.95 }}\r\n                >\r\n                  <motion.div \r\n                    className=\"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center\"\r\n                    whileHover={{ scale: 1.1 }}\r\n                  >\r\n                    {isPlaying ? (\r\n                      <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"white\">\r\n                        <rect x=\"6\" y=\"4\" width=\"4\" height=\"16\"/>\r\n                        <rect x=\"14\" y=\"4\" width=\"4\" height=\"16\"/>\r\n                      </svg>\r\n                    ) : (\r\n                      <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"white\">\r\n                        <polygon points=\"5,3 19,12 5,21\"/>\r\n                      </svg>\r\n                    )}\r\n                  </motion.div>\r\n                </motion.button>\r\n              </div>\r\n            </motion.div>\r\n          </motion.div>\r\n\r\n          {/* Right Column - Content */}\r\n          <motion.div \r\n            className=\"space-y-8\"\r\n            initial={{ opacity: 0, x: 50 }}\r\n            animate={inView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}\r\n            transition={{ duration: 0.8, delay: 0.4 }}\r\n          >\r\n            {/* Description Text */}\r\n            <motion.p \r\n              className=\"text-lg lg:text-xl leading-relaxed text-gray-800 mb-12\"\r\n              style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}\r\n              initial={{ opacity: 0, y: 30 }}\r\n              animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n              transition={{ duration: 0.6, delay: 0.6 }}\r\n            >\r\n              Lusion is a digital production studio that brings your ideas to life \r\n              through visually captivating designs and interactive experiences. \r\n              With our talented team, we push the boundaries by solving \r\n              complex problems, delivering tailored solutions that exceed \r\n              expectations and engage audiences.\r\n            </motion.p>\r\n\r\n            {/* About Us Button - Enhanced with arrow */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 30 }}\r\n              animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\r\n              transition={{ duration: 0.6, delay: 0.8 }}\r\n            >\r\n              <motion.button\r\n                className=\"relative bg-white border border-gray-300 rounded-full px-8 py-4 flex items-center gap-3 text-black font-medium text-lg overflow-hidden group\"\r\n                style={{ fontFamily: 'Aeonik-Medium, sans-serif', minWidth: '160px' }}\r\n                onHoverStart={() => setIsAboutHovered(true)}\r\n                onHoverEnd={() => setIsAboutHovered(false)}\r\n                whileHover={{ scale: 1.02 }}\r\n                transition={{ duration: 0.3 }}\r\n              >\r\n                {/* Fast blue fill background */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 bg-blue-600 rounded-full\"\r\n                  initial={{ x: '-100%' }}\r\n                  animate={{\r\n                    x: isAboutHovered ? '0%' : '-100%',\r\n                  }}\r\n                  transition={{ duration: 0.2, ease: \"easeOut\" }}\r\n                />\r\n                \r\n                {/* Black dot indicator */}\r\n                <motion.div \r\n                  className=\"relative z-10 w-2 h-2 bg-black rounded-full\"\r\n                  animate={{\r\n                    backgroundColor: isAboutHovered ? \"#ffffff\" : \"#000000\"\r\n                  }}\r\n                  transition={{ duration: 0.2 }}\r\n                />\r\n                \r\n                {/* Text with slide left animation */}\r\n                <motion.span \r\n                  className=\"relative z-10\"\r\n                  animate={{\r\n                    color: isAboutHovered ? \"#ffffff\" : \"#000000\",\r\n                    x: isAboutHovered ? -10 : 0\r\n                  }}\r\n                  transition={{ duration: 0.2 }}\r\n                >\r\n                  ABOUT US\r\n                </motion.span>\r\n\r\n                {/* Arrow appearing from right */}\r\n                <motion.div\r\n                  className=\"relative z-10 flex items-center justify-center\"\r\n                  initial={{ x: 30, opacity: 0 }}\r\n                  animate={{\r\n                    x: isAboutHovered ? 0 : 30,\r\n                    opacity: isAboutHovered ? 1 : 0\r\n                  }}\r\n                  transition={{ duration: 0.2, ease: \"easeOut\" }}\r\n                >\r\n                  <svg \r\n                    width=\"20\" \r\n                    height=\"20\" \r\n                    viewBox=\"0 0 24 24\" \r\n                    fill=\"none\" \r\n                    className=\"stroke-current\"\r\n                    strokeWidth=\"2\"\r\n                  >\r\n                    <motion.path\r\n                      d=\"M5 12h14M12 5l7 7-7 7\"\r\n                      animate={{\r\n                        stroke: isAboutHovered ? \"#ffffff\" : \"#000000\"\r\n                      }}\r\n                      transition={{ duration: 0.2 }}\r\n                    />\r\n                  </svg>\r\n                </motion.div>\r\n              </motion.button>\r\n            </motion.div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Intro;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,QAAQ;IACZ,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,WAAW;QACX,aAAa;IACf;IAEA,+CAA+C;IAC/C,MAAM,eAAe,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAC3B,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAI,EAAE;QAAC;QAAG;KAAE,GAC9C;QAAE,WAAW;QAAK,SAAS;IAAG;IAGhC,4DAA4D;IAC5D,MAAM,aAAa,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAI,EAAE;QAAC;QAAK;KAAE;IACnE,MAAM,aAAa,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAK;KAAI,EAAE;QAAC;QAAG;KAAI;IACrE,MAAM,mBAAmB,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAK;KAAI,EAAE;QAAC;QAAG;KAAE,GAAG,8BAA8B;IAE1G,MAAM,cAAc,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAM;KAAK,EAAE;QAAC,CAAC;QAAK;KAAE,GAAG,sBAAsB;IAElG,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAQ;oBACR,qBAAoB;8BAEpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,MAAK;wBACL,eAAc;wBACd,YAAY;wBACZ,OAAO;4BACL,YAAY;wBACd;;;;;;;;;;;;;;;;0BAKN,8OAAC;gBAAI,KAAK;gBAAK,WAAU;0BACvB,cAAA,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9D,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAGxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,WAAU;8CACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,OAAO;4CAAE,YAAY;wCAA4B;;0DAGjD,8OAAC;gDAAI,WAAU;gDACV,OAAO;oDACL,UAAU,CAAC,aAAa,EAAE,KAAK,GAAG,CAAC,gBAAgB,GAAG,KAAK,KAAK,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,gBAAgB,GAAG,KAAK,KAAK,KAAK,eAAe,CAAC;gDACzI;;;;;;0DAIL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,OAAO;oDACL,GAAG;gDACL;gDACA,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAOL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;8CAG5B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DACP,YAAY;gEACV;gEACA;gEACA;gEACA;6DACD;wDACH;wDACA,YAAY;4DACV,UAAU;4DACV,QAAQ;4DACR,MAAM;wDACR;;;;;;kEAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DACP,GAAG;gEAAC;gEAAG,CAAC;gEAAI;6DAAE;4DACd,QAAQ;gEAAC;gEAAG;gEAAK;6DAAI;wDACvB;wDACA,YAAY;4DACV,UAAU;4DACV,QAAQ;4DACR,MAAM;wDACR;;;;;;kEAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DACP,GAAG;gEAAC;gEAAG;gEAAI;6DAAE;4DACb,GAAG;gEAAC;gEAAG;gEAAI;6DAAE;wDACf;wDACA,YAAY;4DACV,UAAU;4DACV,QAAQ;4DACR,MAAM;wDACR;;;;;;kEAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DACP,QAAQ;gEAAC;gEAAG;6DAAI;4DAChB,OAAO;gEAAC;gEAAG;gEAAK;6DAAE;wDACpB;wDACA,YAAY;4DACV,UAAU;4DACV,QAAQ;4DACR,MAAM;wDACR;;;;;;;;;;;;0DAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,WAAU;gDACV,SAAS,IAAM,aAAa,CAAC;gDAC7B,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;0DAExB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAI;8DAExB,0BACC,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;;0EACnD,8OAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;;;;;;0EACnC,8OAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;;;;;;;;;;;iHAGtC,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;kEACnD,cAAA,8OAAC;4DAAQ,QAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAU9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7D,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAGxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,OAAO;wCAAE,YAAY;oCAA6B;oCAClD,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7D,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CACzC;;;;;;8CASD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7D,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,OAAO;4CAAE,YAAY;4CAA6B,UAAU;wCAAQ;wCACpE,cAAc,IAAM,kBAAkB;wCACtC,YAAY,IAAM,kBAAkB;wCACpC,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;;0DAG5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,GAAG;gDAAQ;gDACtB,SAAS;oDACP,GAAG,iBAAiB,OAAO;gDAC7B;gDACA,YAAY;oDAAE,UAAU;oDAAK,MAAM;gDAAU;;;;;;0DAI/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDACP,iBAAiB,iBAAiB,YAAY;gDAChD;gDACA,YAAY;oDAAE,UAAU;gDAAI;;;;;;0DAI9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gDACV,WAAU;gDACV,SAAS;oDACP,OAAO,iBAAiB,YAAY;oDACpC,GAAG,iBAAiB,CAAC,KAAK;gDAC5B;gDACA,YAAY;oDAAE,UAAU;gDAAI;0DAC7B;;;;;;0DAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,GAAG;oDAAI,SAAS;gDAAE;gDAC7B,SAAS;oDACP,GAAG,iBAAiB,IAAI;oDACxB,SAAS,iBAAiB,IAAI;gDAChC;gDACA,YAAY;oDAAE,UAAU;oDAAK,MAAM;gDAAU;0DAE7C,cAAA,8OAAC;oDACC,OAAM;oDACN,QAAO;oDACP,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,aAAY;8DAEZ,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wDACV,GAAE;wDACF,SAAS;4DACP,QAAQ,iBAAiB,YAAY;wDACvC;wDACA,YAAY;4DAAE,UAAU;wDAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlD;uCAEe", "debugId": null}}, {"offset": {"line": 1883, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Loading from \"@/components/Loading\";\nimport Hero from \"@/components/Hero\";\nimport Intro from \"@/components/Intro\";\n\nexport default function Home() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  if (isLoading) {\n    return <Loading onLoadingComplete={() => setIsLoading(false)} />;\n  }\n\n  return (\n    <div className=\"bg-white text-black min-h-screen opacity-0 animate-fadeIn\" style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}>\n      <Hero />\n      <Intro />\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,WAAW;QACb,qBAAO,8OAAC,6HAAA,CAAA,UAAO;YAAC,mBAAmB,IAAM,aAAa;;;;;;IACxD;IAEA,qBACE,8OAAC;QAAI,WAAU;QAA4D,OAAO;YAAE,YAAY;QAA6B;;0BAC3H,8OAAC,0HAAA,CAAA,UAAI;;;;;0BACL,8OAAC,2HAAA,CAAA,UAAK;;;;;;;;;;;AAGZ", "debugId": null}}]}